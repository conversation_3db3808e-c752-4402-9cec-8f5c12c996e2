import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>B<PERSON>,
  <PERSON>,
  Separator,
  Text,
  UserMenu as UserProfile,
  useModalControl,
  ModalClose
} from "@snap/design-system";
import { formatIsoDate, getInitials } from "~/helpers";
import { USER_CONSTANTS } from "~/helpers/constants";
import { useUserData } from "~/store/userStore";
import { Too<PERSON>ip, TooltipTrigger, TooltipContent } from "./ui/tooltip";
import { Info } from "lucide-react";

interface UserMenuProps {
  userMenuContent: React.ReactNode;
}

export default function UserMenu({ userMenuContent }: UserMenuProps) {
  const { open } = useModalControl();
  const userData = useUserData();
  const userImage = userData?.[USER_CONSTANTS.user_data.image as keyof typeof userData] as string;
  const userName = userData?.[USER_CONSTANTS.user_data.name as keyof typeof userData] as string;
  const userCredits = userData?.[USER_CONSTANTS.user_data.credits_minimun as keyof typeof userData] as number;
  const initials = getInitials(userName);
  const userCreditsResetDate = userData?.[USER_CONSTANTS.user_data.next_reset_credits as keyof typeof userData] as string;
  const userProfile = userData?.[USER_CONSTANTS.user_data.role as keyof typeof userData] as string;
  const isUserStandalone = userProfile === "standalone";

  const getValidImageUrl = (image: string | null | undefined) => {
    return image || undefined;
  };

  const renderConsultasInfo = () => {
    return (
      <Tooltip delayDuration={0}>
        <TooltipTrigger className="cursor-help" asChild>
          <Info size={14} className="text-accent" />
        </TooltipTrigger>
        <TooltipContent side="bottom" className="!p-0 !bg-transparent !shadow-none !border-none max-w-[288px]">
          <ChamferBox corner="topLeft" className="rounded-md px-0.5 pt-0.5 bg-neutral-800">
            <div className="relative z-10">
              <Text className="text-left text-netral-100">
                Você possui <span className="font-bold"><span className="text-accent">*</span>{` ${userCredits} consultas `}</span>
              </Text>
              <Text className="text-left text-netral-100">
                válidas até a data <span className="font-semibold">{formatIsoDate(userCreditsResetDate, true)}</span>.
              </Text>
              {
                isUserStandalone && (
                  <div>
                    <Separator className="my-2 border-border" />
                    <Text className="text-left text-neutral-200">
                      <span className="font-bold text-accent">*</span> {`Saiba mais sobre as consultas disponíveis `}
                      <span className="text-accent underline cursor-pointer" onClick={handleOpenConsultasInfo} title="Abrir mais informações de consultas">
                        clicando aqui.
                      </span>
                    </Text>

                  </div>
                )
              }
            </div>
          </ChamferBox>
        </TooltipContent>
      </Tooltip>
    );
  };

  const handleOpenConsultasInfo = () => {
    open({
      modal: () => ({
        title: "INFORMAÇÕES SOBRE CONSULTAS",
        content: <div className="space-y-4 text-neutral-200">
          <p className="text-left font-bold">
            As consultas mensais são compartilhadas entre todos os usuários da <span className="font-bold text-accent">organização</span>.
          </p>
          <p className="font-bold">
            O administrador pode definir um limite individual para cada usuário, mas o saldo real disponível também depende do total restante da organização. Ou seja, mesmo que você tenha uma cota individual de 100 consultas, se a organização tiver apenas 10 consultas restantes no mês, você verá 10 como seu saldo disponível.
          </p>
          <Separator className="my-2 border-border" />
          <Text variant="body-md" className="text-left mt-1 font-bold">
            Exemplo:
          </Text>
          <ul className="list-disc list-inside ml-4">
            <li>
              <strong>Organização possui um total de 100 consultas/mês.</strong>
            </li>
            <li>
              <strong>Cenário A - Cotas não definidas:</strong> Todos os usuários compartilham o mesmo saldo. Se outro usuário realizar uma consulta, seu saldo também será reduzido — de 100 para 99, por exemplo.
            </li>
            <li>
              <strong>Cenário B - Cotas definidas por usuário:</strong> Cada usuário tem uma cota fixa (ex: 10 consultas para cada um dos 10 usuários).
              No entanto, seu saldo individual nunca será maior do que o total restante da organização.
              Se sua cota for 10, mas a organização tiver apenas 3 consultas restantes, seu saldo será 3.
            </li>
          </ul>
        </div>,
        footer: <ModalClose><Button>Fechar</Button></ModalClose>,
      }),
      config: {
        content: {
          className: "max-w-xl",
        },
      },
    });
  };

  const renderUserProfile = () => {
    const profileProps = {
      Profile: (
        <div className="flex items-center gap-3 w-full">
          <Avatar
            size="sm"
            className="size-9"
            src={getValidImageUrl(userImage)}
            fallback={initials || "NA"}
            textAlign="left"
          />
          <List className="space-y-0.5 items-start">
            <span className="text-sm leading-tight">
              {userName || "Sem nome"}
            </span>
            <Separator />
            <div className="flex items-end gap-2">
              <Text className="opacity-80">
                {`${userCredits || 0} Consultas`}
              </Text>
              {renderConsultasInfo()}
            </div>
          </List>
        </div>
      ),
      Menu: userMenuContent,
    };

    return <UserProfile {...profileProps} menuClassName="py-0 px-0" />;
  };

  return renderUserProfile();
}
